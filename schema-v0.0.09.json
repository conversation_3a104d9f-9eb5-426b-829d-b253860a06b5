[{"id": "_pb_users_auth_", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "users", "type": "auth", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cost": 0, "hidden": true, "id": "password901924565", "max": 0, "min": 8, "name": "password", "pattern": "", "presentable": false, "required": true, "system": true, "type": "password"}, {"autogeneratePattern": "[a-zA-Z0-9]{50}", "hidden": true, "id": "text2504183744", "max": 60, "min": 30, "name": "<PERSON><PERSON><PERSON>", "pattern": "", "presentable": false, "primaryKey": false, "required": true, "system": true, "type": "text"}, {"exceptDomains": null, "hidden": false, "id": "email3885137012", "name": "email", "onlyDomains": null, "presentable": false, "required": true, "system": true, "type": "email"}, {"hidden": false, "id": "bool1547992806", "name": "emailVisibility", "presentable": false, "required": false, "system": true, "type": "bool"}, {"hidden": false, "id": "bool256245529", "name": "verified", "presentable": false, "required": false, "system": true, "type": "bool"}, {"autogeneratePattern": "", "hidden": false, "id": "text1579384326", "max": 255, "min": 0, "name": "name", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "file376926767", "maxSelect": 1, "maxSize": 0, "mimeTypes": ["image/jpeg", "image/png", "image/svg+xml", "image/gif", "image/webp"], "name": "avatar", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": null, "type": "file"}, {"autogeneratePattern": "", "hidden": false, "id": "text2208304744", "max": 0, "min": 0, "name": "firstname", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text824489398", "max": 0, "min": 0, "name": "lastname", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text4166911607", "max": 0, "min": 0, "name": "username", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "select1466534506", "maxSelect": 1, "name": "role", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Root", "GOLMod", "GOLStaff", "Merchant", "Customer"]}, {"hidden": false, "id": "number1146066909", "max": null, "min": null, "name": "phone", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": ["CREATE UNIQUE INDEX `idx_tokenKey__pb_users_auth_` ON `users` (`tokenKey`)", "CREATE UNIQUE INDEX `idx_email__pb_users_auth_` ON `users` (`email`) WHERE `email` != ''", "CREATE UNIQUE INDEX `idx_1spF9e9g0x` ON `users` (`username`)"], "system": false, "authRule": "", "manageRule": null, "authAlert": {"enabled": true, "emailTemplate": {"subject": "Login from a new location", "body": "<p>Hello,</p>\n<p>We noticed a login to your {APP_NAME} account from a new location.</p>\n<p>If this was you, you may disregard this email.</p>\n<p><strong>If this wasn't you, you should immediately change your {APP_NAME} account password to revoke access from all other locations.</strong></p>\n<p>\n  Thanks,<br/>\n  {APP_NAME} team\n</p>"}}, "oauth2": {"mappedFields": {"id": "", "name": "name", "username": "", "avatarURL": "avatar"}, "enabled": false}, "passwordAuth": {"enabled": true, "identityFields": ["email", "username"]}, "mfa": {"enabled": false, "duration": 1800, "rule": ""}, "otp": {"enabled": false, "duration": 180, "length": 8, "emailTemplate": {"subject": "OTP for {APP_NAME}", "body": "<main style=\"background-color: #ffffff; font-family: HelveticaNeue,Helvetica,Arial,sans-serif; margin: 0; padding: 0;\">\n    <div style=\"background-color: #ffffff; border: 1px solid #eee; border-radius: 5px; box-shadow: 0 5px 10px rgba(20,50,70,.2); margin-top: 20px; max-width: 360px; margin: 0 auto; padding: 68px 0 130px;\">\n        <img \n            src=\"https://linkmylogistics.com/logo.png\" \n            width=\"200\" \n            height=\"60\" \n            alt=\"Link My Logistics\" \n            style=\"margin: 0 auto; display: block;\"\n        />\n        \n        <p style=\"color: #0a85ea; font-size: 11px; font-weight: 700; font-family: HelveticaNeue,Helvetica,Arial,sans-serif; height: 16px; letter-spacing: 0; line-height: 16px; margin: 16px 8px 8px 8px; text-transform: uppercase; text-align: center;\">\n            Verify Your Identity\n        </p>\n        \n        <h1 style=\"color: #000; display: inline-block; font-family: HelveticaNeue-Medium,Helvetica,Arial,sans-serif; font-size: 20px; font-weight: 500; line-height: 24px; margin-bottom: 0; margin-top: 0; text-align: center; width: 100%;\">\n            Enter the following code to finish setting up your account.\n        </h1>\n        \n        <div style=\"background: rgba(0,0,0,.05); border-radius: 4px; margin: 16px auto 14px; vertical-align: middle; width: 280px;\">\n            <p style=\"color: #000; font-family: HelveticaNeue-Bold; font-size: 32px; font-weight: 700; letter-spacing: 6px; line-height: 40px; padding-bottom: 8px; padding-top: 8px; margin: 0 auto; display: block; text-align: center;\">\n                {OTP}\n            </p>\n        </div>\n        \n        <p style=\"color: #444; font-size: 15px; font-family: HelveticaNeue,Helvetica,Arial,sans-serif; letter-spacing: 0; line-height: 23px; padding: 0 40px; margin: 0; text-align: center;\">\n            Not expecting this email?\n        </p>\n        \n        <p style=\"color: #444; font-size: 15px; font-family: HelveticaNeue,Helvetica,Arial,sans-serif; letter-spacing: 0; line-height: 23px; padding: 0 40px; margin: 0; text-align: center;\">\n            Contact \n            <a href=\"mailto:<EMAIL>\" style=\"color: #444; text-decoration: underline;\">\n                <EMAIL>\n            </a> \n            if you did not request this code.\n        </p>\n    </div>\n    \n    <p style=\"color: #000; font-size: 12px; font-weight: 800; letter-spacing: 0; line-height: 23px; margin: 0; margin-top: 20px; font-family: HelveticaNeue,Helvetica,Arial,sans-serif; text-align: center; text-transform: uppercase;\">\n        Securely powered by {APP_NAME}.\n    </p>\n</main>"}}, "authToken": {"duration": 604800}, "passwordResetToken": {"duration": 1800}, "emailChangeToken": {"duration": 1800}, "verificationToken": {"duration": 259200}, "fileToken": {"duration": 180}, "verificationTemplate": {"subject": "Verify your {APP_NAME} email", "body": "<p>Hello,</p>\n<p>Thank you for joining us at {APP_NAME}.</p>\n<p>Click on the button below to verify your email address.</p>\n<p>\n  <a class=\"btn\" href=\"{APP_URL}/_/#/auth/confirm-verification/{TOKEN}\" target=\"_blank\" rel=\"noopener\">Verify</a>\n</p>\n<p>\n  Thanks,<br/>\n  {APP_NAME} team\n</p>"}, "resetPasswordTemplate": {"subject": "Reset your {APP_NAME} password", "body": "<p>Hello,</p>\n<p>Click on the button below to reset your password.</p>\n<p>\n  <a class=\"btn\" href=\"{APP_URL}/_/#/auth/confirm-password-reset/{TOKEN}\" target=\"_blank\" rel=\"noopener\">Reset password</a>\n</p>\n<p><i>If you didn't ask to reset your password, you can ignore this email.</i></p>\n<p>\n  Thanks,<br/>\n  {APP_NAME} team\n</p>"}, "confirmEmailChangeTemplate": {"subject": "Confirm your {APP_NAME} new email address", "body": "<p>Hello,</p>\n<p>Click on the button below to confirm your new email address.</p>\n<p>\n  <a class=\"btn\" href=\"{APP_URL}/_/#/auth/confirm-email-change/{TOKEN}\" target=\"_blank\" rel=\"noopener\">Confirm new email</a>\n</p>\n<p><i>If you didn't ask to change your email address, you can ignore this email.</i></p>\n<p>\n  Thanks,<br/>\n  {APP_NAME} team\n</p>"}}, {"id": "pbc_2014615706", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "3pl_job_order", "type": "base", "fields": [{"autogeneratePattern": "JOB-[0-9]{11}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "JOB-[0-9]{3,11}", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_1751765743", "hidden": false, "id": "relation4113142680", "maxSelect": 1, "minSelect": 0, "name": "order", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "date3149528700", "max": "", "min": "", "name": "fromDate", "presentable": false, "required": false, "system": false, "type": "date"}, {"hidden": false, "id": "date301681072", "max": "", "min": "", "name": "toDate", "presentable": false, "required": false, "system": false, "type": "date"}, {"cascadeDelete": false, "collectionId": "pbc_863811952", "hidden": false, "id": "relation3785202386", "maxSelect": 1, "minSelect": 0, "name": "service", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_2651147062", "hidden": false, "id": "relation1138201242", "maxSelect": 1, "minSelect": 0, "name": "serviceType", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_1864144027", "hidden": false, "id": "relation2444010507", "maxSelect": 999, "minSelect": 0, "name": "containers", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text1156222427", "max": 0, "min": 0, "name": "remarks", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "file104153177", "maxSelect": 99, "maxSize": 0, "mimeTypes": [], "name": "files", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": [], "type": "file"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Pending", "Accepted", "Rejected", "In Progress", "Completed"]}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation3545646658", "maxSelect": 1, "minSelect": 0, "name": "created<PERSON>y", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_1879942143", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "3pl_order_movement", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_1751765743", "hidden": false, "id": "relation4113142680", "maxSelect": 1, "minSelect": 0, "name": "order", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_863811952", "hidden": false, "id": "relation3785202386", "maxSelect": 1, "minSelect": 0, "name": "service", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "date2862495610", "max": "", "min": "", "name": "date", "presentable": false, "required": false, "system": false, "type": "date"}, {"autogeneratePattern": "", "hidden": false, "id": "text1156222427", "max": 0, "min": 0, "name": "remarks", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "file104153177", "maxSelect": 99, "maxSize": 0, "mimeTypes": [], "name": "files", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": [], "type": "file"}, {"autogeneratePattern": "", "hidden": false, "id": "text2063623452", "max": 0, "min": 0, "name": "status", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_1751765743", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "3pl_orders", "type": "base", "fields": [{"autogeneratePattern": "ORD-[0-9]{11}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "ORD-[0-9]{3,11}", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text118057898", "max": 0, "min": 0, "name": "igmNo", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1112657042", "max": 0, "min": 0, "name": "blNo", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text3145606124", "max": 0, "min": 0, "name": "itemNo", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_1864144027", "hidden": false, "id": "relation2444010507", "maxSelect": 999, "minSelect": 0, "name": "containers", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text3733864923", "max": 0, "min": 0, "name": "consignee<PERSON><PERSON>", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text3096597736", "max": 0, "min": 0, "name": "cha<PERSON>ame", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_3190968249", "hidden": false, "id": "relation2462348188", "maxSelect": 1, "minSelect": 0, "name": "provider", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2168032777", "maxSelect": 1, "minSelect": 0, "name": "customer", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "date1269603864", "max": "", "min": "", "name": "startDate", "presentable": false, "required": false, "system": false, "type": "date"}, {"autogeneratePattern": "", "hidden": false, "id": "text593843763", "max": 0, "min": 0, "name": "startLocation", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "date826688707", "max": "", "min": "", "name": "endDate", "presentable": false, "required": false, "system": false, "type": "date"}, {"autogeneratePattern": "", "hidden": false, "id": "text1181702921", "max": 0, "min": 0, "name": "endLocation", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text870208694", "max": 0, "min": 0, "name": "serviceRequest", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text228700180", "max": 0, "min": 0, "name": "vehicleDescription", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1005175516", "max": 0, "min": 0, "name": "orderDescription", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation3545646658", "maxSelect": 1, "minSelect": 0, "name": "created<PERSON>y", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Pending", "Accepted", "Rejected", "In Progress", "Completed"]}, {"hidden": false, "id": "file104153177", "maxSelect": 99, "maxSize": 100000000, "mimeTypes": [], "name": "files", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": [], "type": "file"}, {"hidden": false, "id": "bool1531251113", "name": "merchantVerified", "presentable": false, "required": false, "system": false, "type": "bool"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2891322017", "maxSelect": 1, "minSelect": 0, "name": "merchantVerifiedBy", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "bool465722684", "name": "golVerified", "presentable": false, "required": false, "system": false, "type": "bool"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation3429284244", "maxSelect": 1, "minSelect": 0, "name": "golVerifiedBy", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text1001949196", "max": 0, "min": 0, "name": "reason", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": ["CREATE UNIQUE INDEX `idx_YwCZEZ3gVw` ON `3pl_orders` (\n  `igmNo`,\n  `blNo`,\n  `itemNo`\n)"], "system": false}, {"id": "pbc_1219764873", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "3pl_pricing_request", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2375276105", "maxSelect": 1, "minSelect": 0, "name": "user", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_3190968249", "hidden": false, "id": "relation3497959979", "maxSelect": 1, "minSelect": 0, "name": "serviceProvider", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "date1269603864", "max": "", "min": "", "name": "startDate", "presentable": false, "required": false, "system": false, "type": "date"}, {"autogeneratePattern": "", "hidden": false, "id": "text593843763", "max": 0, "min": 0, "name": "startLocation", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1181702921", "max": 0, "min": 0, "name": "endLocation", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text208543272", "max": 0, "min": 0, "name": "specialRequest", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "bool465722684", "name": "golVerified", "presentable": false, "required": false, "system": false, "type": "bool"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation3429284244", "maxSelect": 1, "minSelect": 0, "name": "golVerifiedBy", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "select2453506101", "maxSelect": 1, "name": "containerType", "presentable": false, "required": false, "system": false, "type": "select", "values": ["General", "ODC/FR/OT", "<PERSON><PERSON>", "Mix"]}, {"hidden": false, "id": "select156841218", "maxSelect": 1, "name": "delayType", "presentable": false, "required": false, "system": false, "type": "select", "values": ["DPD", "Non-DPD"]}, {"hidden": false, "id": "number1885463975", "max": null, "min": null, "name": "preferableRate", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "number1241718566", "max": null, "min": null, "name": "containersPerMonth", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Pending", "Accepted", "Rejected", "In Progress", "Completed"]}, {"autogeneratePattern": "", "hidden": false, "id": "text1001949196", "max": 0, "min": 0, "name": "reason", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_259685098", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "3pl_service_details", "type": "base", "fields": [{"autogeneratePattern": "REC-[0-9]{11}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "REC-[0-9]{3,11}", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_1751765743", "hidden": false, "id": "relation4113142680", "maxSelect": 1, "minSelect": 0, "name": "order", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_2014615706", "hidden": false, "id": "relation3993090758", "maxSelect": 1, "minSelect": 0, "name": "jobOrder", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_1864144027", "hidden": false, "id": "relation3349343259", "maxSelect": 1, "minSelect": 0, "name": "container", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_863811952", "hidden": false, "id": "relation3785202386", "maxSelect": 1, "minSelect": 0, "name": "service", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_2651147062", "hidden": false, "id": "relation2363381545", "maxSelect": 1, "minSelect": 0, "name": "type", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text646683805", "max": 0, "min": 0, "name": "agent", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "date2862495610", "max": "", "min": "", "name": "date", "presentable": false, "required": false, "system": false, "type": "date"}, {"autogeneratePattern": "", "hidden": false, "id": "text1562571485", "max": 0, "min": 0, "name": "receiptNo", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1156222427", "max": 0, "min": 0, "name": "remarks", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "file104153177", "maxSelect": 99, "maxSize": 0, "mimeTypes": [], "name": "files", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": [], "type": "file"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Pending", "Accepted", "Rejected", "In Progress", "Completed"]}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_910879608", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "3pl_service_requests", "type": "base", "fields": [{"autogeneratePattern": "REQ-[0-9]{11}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "REQ-[0-9]{3,11}", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2375276105", "maxSelect": 1, "minSelect": 0, "name": "user", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_1751765743", "hidden": false, "id": "relation4113142680", "maxSelect": 1, "minSelect": 0, "name": "order", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_863811952", "hidden": false, "id": "relation3785202386", "maxSelect": 1, "minSelect": 0, "name": "service", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_2651147062", "hidden": false, "id": "relation1138201242", "maxSelect": 1, "minSelect": 0, "name": "serviceType", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "bool1531251113", "name": "merchantVerified", "presentable": false, "required": false, "system": false, "type": "bool"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2891322017", "maxSelect": 1, "minSelect": 0, "name": "merchantVerifiedBy", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "bool465722684", "name": "golVerified", "presentable": false, "required": false, "system": false, "type": "bool"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation3429284244", "maxSelect": 1, "minSelect": 0, "name": "golVerifiedBy", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text3282145173", "max": 0, "min": 0, "name": "customerRemarks", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1001949196", "max": 0, "min": 0, "name": "reason", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Pending", "Accepted", "Rejected", "In Progress", "Completed"]}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_283183114", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "3pl_tariffs_request", "type": "base", "fields": [{"autogeneratePattern": "REQ-[0-9]{11}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "REQ-[0-9]{3,11}", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_1751765743", "hidden": false, "id": "relation4113142680", "maxSelect": 1, "minSelect": 0, "name": "order", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_1864144027", "hidden": false, "id": "relation3349343259", "maxSelect": 1, "minSelect": 0, "name": "container", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_863811952", "hidden": false, "id": "relation3785202386", "maxSelect": 1, "minSelect": 0, "name": "service", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "select2363381545", "maxSelect": 1, "name": "type", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Loaded", "<PERSON><PERSON><PERSON>"]}, {"hidden": false, "id": "date3149528700", "max": "", "min": "", "name": "fromDate", "presentable": false, "required": false, "system": false, "type": "date"}, {"hidden": false, "id": "date301681072", "max": "", "min": "", "name": "toDate", "presentable": false, "required": false, "system": false, "type": "date"}, {"autogeneratePattern": "", "hidden": false, "id": "text1156222427", "max": 0, "min": 0, "name": "remarks", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1001949196", "max": 0, "min": 0, "name": "reason", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "file104153177", "maxSelect": 99, "maxSize": 0, "mimeTypes": [], "name": "files", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": [], "type": "file"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Pending", "Accepted", "Rejected", "In Progress", "Completed"]}, {"hidden": false, "id": "bool1531251113", "name": "merchantVerified", "presentable": false, "required": false, "system": false, "type": "bool"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2891322017", "maxSelect": 1, "minSelect": 0, "name": "merchantVerifiedBy", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation3429284244", "maxSelect": 1, "minSelect": 0, "name": "golVerifiedBy", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "bool465722684", "name": "golVerified", "presentable": false, "required": false, "system": false, "type": "bool"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_3029083964", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "3pl_transport_movement", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_1751765743", "hidden": false, "id": "relation4113142680", "maxSelect": 1, "minSelect": 0, "name": "order", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_2014615706", "hidden": false, "id": "relation3993090758", "maxSelect": 1, "minSelect": 0, "name": "jobOrder", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_1602236899", "hidden": false, "id": "relation461431942", "maxSelect": 1, "minSelect": 0, "name": "vehicle", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "json291929305", "maxSize": 0, "name": "driver", "presentable": false, "required": false, "system": false, "type": "json"}, {"hidden": false, "id": "date1269603864", "max": "", "min": "", "name": "startDate", "presentable": false, "required": false, "system": false, "type": "date"}, {"hidden": false, "id": "date826688707", "max": "", "min": "", "name": "endDate", "presentable": false, "required": false, "system": false, "type": "date"}, {"hidden": false, "id": "geoPoint593843763", "name": "startLocation", "presentable": false, "required": false, "system": false, "type": "geoPoint"}, {"hidden": false, "id": "geoPoint2041417573", "name": "currentLocation", "presentable": false, "required": false, "system": false, "type": "geoPoint"}, {"hidden": false, "id": "geoPoint1181702921", "name": "endLocation", "presentable": false, "required": false, "system": false, "type": "geoPoint"}, {"autogeneratePattern": "", "hidden": false, "id": "text1156222427", "max": 0, "min": 0, "name": "remarks", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Not Started", "In Transit", "Delivered", "Cancelled"]}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_3222384912", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "allowed_service_providers", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_863811952", "hidden": false, "id": "relation3785202386", "maxSelect": 999, "minSelect": 0, "name": "service", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_3190968249", "hidden": false, "id": "relation2462348188", "maxSelect": 1, "minSelect": 0, "name": "provider", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2375276105", "maxSelect": 1, "minSelect": 0, "name": "user", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_2947930106", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "cfs_job_order", "type": "base", "fields": [{"autogeneratePattern": "JOB-[0-9]{11}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "JOB-[0-9]{3,11}", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_2872855771", "hidden": false, "id": "relation4113142680", "maxSelect": 1, "minSelect": 0, "name": "order", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "date3149528700", "max": "", "min": "", "name": "fromDate", "presentable": false, "required": false, "system": false, "type": "date"}, {"hidden": false, "id": "date301681072", "max": "", "min": "", "name": "toDate", "presentable": false, "required": false, "system": false, "type": "date"}, {"cascadeDelete": false, "collectionId": "pbc_2651147062", "hidden": false, "id": "relation1138201242", "maxSelect": 1, "minSelect": 0, "name": "serviceType", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_1864144027", "hidden": false, "id": "relation2444010507", "maxSelect": 999, "minSelect": 0, "name": "containers", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text1156222427", "max": 0, "min": 0, "name": "remarks", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "file104153177", "maxSelect": 99, "maxSize": 0, "mimeTypes": [], "name": "files", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": [], "type": "file"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Pending", "Accepted", "Rejected", "In Progress", "Completed"]}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation3545646658", "maxSelect": 1, "minSelect": 0, "name": "created<PERSON>y", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_1015806038", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "cfs_order_movement", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_2872855771", "hidden": false, "id": "relation4113142680", "maxSelect": 1, "minSelect": 0, "name": "order", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "date2862495610", "max": "", "min": "", "name": "date", "presentable": false, "required": false, "system": false, "type": "date"}, {"autogeneratePattern": "", "hidden": false, "id": "text1156222427", "max": 0, "min": 0, "name": "remarks", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "file104153177", "maxSelect": 99, "maxSize": 0, "mimeTypes": [], "name": "files", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": [], "type": "file"}, {"autogeneratePattern": "", "hidden": false, "id": "text2063623452", "max": 0, "min": 0, "name": "status", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_2872855771", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "cfs_orders", "type": "base", "fields": [{"autogeneratePattern": "ORD-[0-9]{11}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "ORD-[0-9]{3,11}", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text118057898", "max": 0, "min": 0, "name": "igmNo", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1112657042", "max": 0, "min": 0, "name": "blNo", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text3145606124", "max": 0, "min": 0, "name": "itemNo", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_1864144027", "hidden": false, "id": "relation2444010507", "maxSelect": 999, "minSelect": 0, "name": "containers", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text3733864923", "max": 0, "min": 0, "name": "consignee<PERSON><PERSON>", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text3096597736", "max": 0, "min": 0, "name": "cha<PERSON>ame", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_3190968249", "hidden": false, "id": "relation1333477580", "maxSelect": 1, "minSelect": 0, "name": "cfs", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2168032777", "maxSelect": 1, "minSelect": 0, "name": "customer", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "date3149528700", "max": "", "min": "", "name": "fromDate", "presentable": false, "required": false, "system": false, "type": "date"}, {"hidden": false, "id": "date301681072", "max": "", "min": "", "name": "toDate", "presentable": false, "required": false, "system": false, "type": "date"}, {"autogeneratePattern": "", "hidden": false, "id": "text3429085233", "max": 0, "min": 0, "name": "orderDescription", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation3545646658", "maxSelect": 1, "minSelect": 0, "name": "created<PERSON>y", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text1001949196", "max": 0, "min": 0, "name": "reason", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Pending", "Accepted", "Rejected", "In Progress", "Completed"]}, {"hidden": false, "id": "file104153177", "maxSelect": 99, "maxSize": 100000000, "mimeTypes": [], "name": "files", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": [], "type": "file"}, {"hidden": false, "id": "bool1531251113", "name": "merchantVerified", "presentable": false, "required": false, "system": false, "type": "bool"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2891322017", "maxSelect": 1, "minSelect": 0, "name": "merchantVerifiedBy", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "bool465722684", "name": "golVerified", "presentable": false, "required": false, "system": false, "type": "bool"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation3429284244", "maxSelect": 1, "minSelect": 0, "name": "golVerifiedBy", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": ["CREATE UNIQUE INDEX `idx_uLjPoop6Vx` ON `cfs_orders` (\n  `igmNo`,\n  `blNo`,\n  `itemNo`\n)"], "system": false}, {"id": "pbc_3891445662", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "cfs_pricing_request", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2375276105", "maxSelect": 1, "minSelect": 0, "name": "user", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_3190968249", "hidden": false, "id": "relation3497959979", "maxSelect": 1, "minSelect": 0, "name": "serviceProvider", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "bool465722684", "name": "golVerified", "presentable": false, "required": false, "system": false, "type": "bool"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation3429284244", "maxSelect": 1, "minSelect": 0, "name": "golVerifiedBy", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "select2453506101", "maxSelect": 1, "name": "containerType", "presentable": false, "required": false, "system": false, "type": "select", "values": ["General", "ODC/FR/OT", "<PERSON><PERSON>", "Mix"]}, {"hidden": false, "id": "select156841218", "maxSelect": 1, "name": "delayType", "presentable": false, "required": false, "system": false, "type": "select", "values": ["DPD", "Non-DPD"]}, {"hidden": false, "id": "number1885463975", "max": null, "min": null, "name": "preferableRate", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "number1241718566", "max": null, "min": null, "name": "containersPerMonth", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Pending", "Accepted", "Rejected", "In Progress", "Completed"]}, {"autogeneratePattern": "", "hidden": false, "id": "text1001949196", "max": 0, "min": 0, "name": "reason", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_2688346109", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "cfs_service_details", "type": "base", "fields": [{"autogeneratePattern": "REC-[0-9]{11}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "REC-[0-9]{3,11}", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_2872855771", "hidden": false, "id": "relation4113142680", "maxSelect": 1, "minSelect": 0, "name": "order", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_2947930106", "hidden": false, "id": "relation3993090758", "maxSelect": 1, "minSelect": 0, "name": "jobOrder", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_1864144027", "hidden": false, "id": "relation3349343259", "maxSelect": 1, "minSelect": 0, "name": "container", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_2651147062", "hidden": false, "id": "relation2363381545", "maxSelect": 1, "minSelect": 0, "name": "type", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text646683805", "max": 0, "min": 0, "name": "agent", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "date2862495610", "max": "", "min": "", "name": "date", "presentable": false, "required": false, "system": false, "type": "date"}, {"autogeneratePattern": "", "hidden": false, "id": "text1562571485", "max": 0, "min": 0, "name": "receiptNo", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1156222427", "max": 0, "min": 0, "name": "remarks", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "file104153177", "maxSelect": 99, "maxSize": 0, "mimeTypes": [], "name": "files", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": [], "type": "file"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Pending", "Accepted", "Rejected", "In Progress", "Completed"]}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_3040225314", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "cfs_service_requests", "type": "base", "fields": [{"autogeneratePattern": "REQ-[0-9]{11}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "REQ-[0-9]{3,11}", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2375276105", "maxSelect": 1, "minSelect": 0, "name": "user", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_2872855771", "hidden": false, "id": "relation4113142680", "maxSelect": 1, "minSelect": 0, "name": "order", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_2651147062", "hidden": false, "id": "relation1138201242", "maxSelect": 1, "minSelect": 0, "name": "serviceType", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "bool1531251113", "name": "merchantVerified", "presentable": false, "required": false, "system": false, "type": "bool"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2891322017", "maxSelect": 1, "minSelect": 0, "name": "merchantVerifiedBy", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text3282145173", "max": 0, "min": 0, "name": "customerRemarks", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1001949196", "max": 0, "min": 0, "name": "reason", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Pending", "Accepted", "Rejected", "In Progress", "Completed"]}, {"hidden": false, "id": "file104153177", "maxSelect": 99, "maxSize": 100000000, "mimeTypes": [], "name": "files", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": [], "type": "file"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_3215430941", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "cfs_tariffs_request", "type": "base", "fields": [{"autogeneratePattern": "REQ-[0-9]{11}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "REQ-[0-9]{3,11}", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_2872855771", "hidden": false, "id": "relation4113142680", "maxSelect": 1, "minSelect": 0, "name": "order", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_1864144027", "hidden": false, "id": "relation3349343259", "maxSelect": 1, "minSelect": 0, "name": "container", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "select2363381545", "maxSelect": 1, "name": "type", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Loaded", "<PERSON><PERSON><PERSON>"]}, {"hidden": false, "id": "date3149528700", "max": "", "min": "", "name": "fromDate", "presentable": false, "required": false, "system": false, "type": "date"}, {"hidden": false, "id": "date301681072", "max": "", "min": "", "name": "toDate", "presentable": false, "required": false, "system": false, "type": "date"}, {"autogeneratePattern": "", "hidden": false, "id": "text1156222427", "max": 0, "min": 0, "name": "remarks", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1001949196", "max": 0, "min": 0, "name": "reason", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "file104153177", "maxSelect": 99, "maxSize": 0, "mimeTypes": [], "name": "files", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": [], "type": "file"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Pending", "Accepted", "Rejected", "In Progress", "Completed"]}, {"hidden": false, "id": "bool1531251113", "name": "merchantVerified", "presentable": false, "required": false, "system": false, "type": "bool"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2891322017", "maxSelect": 1, "minSelect": 0, "name": "merchantVerifiedBy", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "bool4657226842", "name": "golVerified", "presentable": false, "required": false, "system": false, "type": "bool"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "bool465722684", "maxSelect": 1, "minSelect": 0, "name": "golVerifiedBy", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_3419561403", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "chat_session", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2375276105", "maxSelect": 1, "minSelect": 0, "name": "user", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation646683805", "maxSelect": 1, "minSelect": 0, "name": "agent", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Open", "Close"]}, {"hidden": false, "id": "date1561543039", "max": "", "min": "", "name": "closed_at", "presentable": false, "required": false, "system": false, "type": "date"}, {"hidden": false, "id": "select1095616328", "maxSelect": 1, "name": "chatType", "presentable": false, "required": false, "system": false, "type": "select", "values": ["support", "client_customer"]}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation3343123541", "maxSelect": 1, "minSelect": 0, "name": "client", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2168032777", "maxSelect": 1, "minSelect": 0, "name": "customer", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text4224597626", "max": 0, "min": 0, "name": "subject", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "select1138201242", "maxSelect": 1, "name": "serviceType", "presentable": false, "required": false, "system": false, "type": "select", "values": ["CFS", "Transport", "3PL", "Warehouse"]}, {"hidden": false, "id": "date2784868587", "max": "", "min": "", "name": "lastMessageAt", "presentable": false, "required": false, "system": false, "type": "date"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_1864144027", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "containers", "type": "base", "fields": [{"autogeneratePattern": "CON-[0-9]{11}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "CON-[0-9]{3,11}", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2013763573", "maxSelect": 1, "minSelect": 0, "name": "ownedBy", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text4070878934", "max": 0, "min": 0, "name": "containerNo", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text4156564586", "max": 0, "min": 0, "name": "size", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Good", "Empty", "Loading", "Loaded", "Damaged", "Missing", "Broken", "COR", "Free", "Busy"]}, {"autogeneratePattern": "", "hidden": false, "id": "text3882496157", "max": 0, "min": 0, "name": "cargoType", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": ["CREATE UNIQUE INDEX `idx_f7G3fmcFWM` ON `containers` (`containerNo`)"], "system": false}, {"id": "pbc_3249699688", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "custom_cfs_job_order", "type": "base", "fields": [{"autogeneratePattern": "JOB-[0-9]{11}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "JOB-[0-9]{3,11}", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_4156512496", "hidden": false, "id": "relation4113142680", "maxSelect": 1, "minSelect": 0, "name": "order", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "date3149528700", "max": "", "min": "", "name": "fromDate", "presentable": false, "required": false, "system": false, "type": "date"}, {"hidden": false, "id": "date301681072", "max": "", "min": "", "name": "toDate", "presentable": false, "required": false, "system": false, "type": "date"}, {"cascadeDelete": false, "collectionId": "pbc_2651147062", "hidden": false, "id": "relation1138201242", "maxSelect": 1, "minSelect": 0, "name": "serviceType", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_1864144027", "hidden": false, "id": "relation2444010507", "maxSelect": 999, "minSelect": 0, "name": "containers", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text1156222427", "max": 0, "min": 0, "name": "remarks", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "file104153177", "maxSelect": 99, "maxSize": 0, "mimeTypes": [], "name": "files", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": [], "type": "file"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Pending", "Accepted", "Rejected", "In Progress", "Completed"]}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation3545646658", "maxSelect": 1, "minSelect": 0, "name": "created<PERSON>y", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_2808996129", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "custom_cfs_order_movement", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_4156512496", "hidden": false, "id": "relation4113142680", "maxSelect": 1, "minSelect": 0, "name": "order", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "date2862495610", "max": "", "min": "", "name": "date", "presentable": false, "required": false, "system": false, "type": "date"}, {"autogeneratePattern": "", "hidden": false, "id": "text1156222427", "max": 0, "min": 0, "name": "remarks", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "file104153177", "maxSelect": 99, "maxSize": 0, "mimeTypes": [], "name": "files", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": [], "type": "file"}, {"autogeneratePattern": "", "hidden": false, "id": "text2063623452", "max": 0, "min": 0, "name": "status", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_4156512496", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "custom_cfs_orders", "type": "base", "fields": [{"autogeneratePattern": "ORD-[0-9]{11}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "ORD-[0-9]{3,11}", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text118057898", "max": 0, "min": 0, "name": "igmNo", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1112657042", "max": 0, "min": 0, "name": "blNo", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1835792446", "max": 0, "min": 0, "name": "itemNo", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_1864144027", "hidden": false, "id": "relation2444010507", "maxSelect": 999, "minSelect": 0, "name": "containers", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text3733864923", "max": 0, "min": 0, "name": "consignee<PERSON><PERSON>", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text3096597736", "max": 0, "min": 0, "name": "cha<PERSON>ame", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_3190968249", "hidden": false, "id": "relation1333477580", "maxSelect": 1, "minSelect": 0, "name": "cfs", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2168032777", "maxSelect": 1, "minSelect": 0, "name": "customer", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "date3149528700", "max": "", "min": "", "name": "fromDate", "presentable": false, "required": false, "system": false, "type": "date"}, {"hidden": false, "id": "date301681072", "max": "", "min": "", "name": "toDate", "presentable": false, "required": false, "system": false, "type": "date"}, {"autogeneratePattern": "", "hidden": false, "id": "text1005175516", "max": 0, "min": 0, "name": "orderDescription", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation3545646658", "maxSelect": 1, "minSelect": 0, "name": "created<PERSON>y", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text1001949196", "max": 0, "min": 0, "name": "reason", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Pending", "Accepted", "Rejected", "In Progress", "Completed"]}, {"hidden": false, "id": "file104153177", "maxSelect": 99, "maxSize": 100000000, "mimeTypes": [], "name": "files", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": [], "type": "file"}, {"hidden": false, "id": "bool1531251113", "name": "merchantVerified", "presentable": false, "required": false, "system": false, "type": "bool"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2891322017", "maxSelect": 1, "minSelect": 0, "name": "merchantVerifiedBy", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "bool465722684", "name": "golVerified", "presentable": false, "required": false, "system": false, "type": "bool"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation3429284244", "maxSelect": 1, "minSelect": 0, "name": "golVerifiedBy", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": ["CREATE UNIQUE INDEX `idx_9xcDiXDqUy` ON `custom_cfs_orders` (\n  `igmNo`,\n  `blNo`,\n  `itemNo`\n)"], "system": false}, {"id": "pbc_2598108337", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "custom_cfs_service_details", "type": "base", "fields": [{"autogeneratePattern": "REC-[0-9]{11}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "REC-[0-9]{3,11}", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_4156512496", "hidden": false, "id": "relation4113142680", "maxSelect": 1, "minSelect": 0, "name": "order", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_3249699688", "hidden": false, "id": "relation3993090758", "maxSelect": 1, "minSelect": 0, "name": "jobOrder", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_1864144027", "hidden": false, "id": "relation3349343259", "maxSelect": 1, "minSelect": 0, "name": "container", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_2651147062", "hidden": false, "id": "relation2363381545", "maxSelect": 1, "minSelect": 0, "name": "type", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text646683805", "max": 0, "min": 0, "name": "agent", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "date2862495610", "max": "", "min": "", "name": "date", "presentable": false, "required": false, "system": false, "type": "date"}, {"autogeneratePattern": "", "hidden": false, "id": "text1562571485", "max": 0, "min": 0, "name": "receiptNo", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1156222427", "max": 0, "min": 0, "name": "remarks", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "file104153177", "maxSelect": 99, "maxSize": 0, "mimeTypes": [], "name": "files", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": [], "type": "file"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Pending", "Accepted", "Rejected", "In Progress", "Completed"]}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_3395732098", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "custom_cfs_service_requests", "type": "base", "fields": [{"autogeneratePattern": "REQ-[0-9]{11}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "REQ-[0-9]{3,11}", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2375276105", "maxSelect": 1, "minSelect": 0, "name": "user", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_4156512496", "hidden": false, "id": "relation4113142680", "maxSelect": 1, "minSelect": 0, "name": "order", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_2651147062", "hidden": false, "id": "relation1138201242", "maxSelect": 1, "minSelect": 0, "name": "serviceType", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "bool1531251113", "name": "merchantVerified", "presentable": false, "required": false, "system": false, "type": "bool"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2891322017", "maxSelect": 1, "minSelect": 0, "name": "merchantVerifiedBy", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text3282145173", "max": 0, "min": 0, "name": "customerRemarks", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1001949196", "max": 0, "min": 0, "name": "reason", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Pending", "Accepted", "Rejected", "In Progress", "Completed"]}, {"hidden": false, "id": "file104153177", "maxSelect": 99, "maxSize": 100000000, "mimeTypes": [], "name": "files", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": [], "type": "file"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_1137287749", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "custom_order_packages", "type": "base", "fields": [{"autogeneratePattern": "PKG-[0-9]{11}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "PKG-[0-9]{3,11}", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text724990059", "max": 0, "min": 0, "name": "title", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1843675174", "max": 0, "min": 0, "name": "description", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_4156512496", "hidden": false, "id": "relation1333477580", "maxSelect": 1, "minSelect": 0, "name": "cfs", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_721042516", "hidden": false, "id": "relation1722491182", "maxSelect": 1, "minSelect": 0, "name": "transport", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_2085983916", "hidden": false, "id": "relation3971189756", "maxSelect": 1, "minSelect": 0, "name": "warehouse", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation3545646658", "maxSelect": 1, "minSelect": 0, "name": "created<PERSON>y", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_271305297", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "custom_packages", "type": "base", "fields": [{"autogeneratePattern": "PKG-[0-9]{11}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "PKG-[0-9]{3,11}", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text724990059", "max": 0, "min": 0, "name": "title", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1843675174", "max": 0, "min": 0, "name": "description", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_863811952", "hidden": false, "id": "relation1932714345", "maxSelect": 999, "minSelect": 0, "name": "services", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_137856443", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "custom_pricing_request", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2375276105", "maxSelect": 1, "minSelect": 0, "name": "user", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_271305297", "hidden": false, "id": "relation3731384213", "maxSelect": 1, "minSelect": 0, "name": "package", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "date1269603864", "max": "", "min": "", "name": "startDate", "presentable": false, "required": false, "system": false, "type": "date"}, {"autogeneratePattern": "", "hidden": false, "id": "text593843763", "max": 0, "min": 0, "name": "startLocation", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1181702921", "max": 0, "min": 0, "name": "endLocation", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text208543272", "max": 0, "min": 0, "name": "specialRequest", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "bool465722684", "name": "golVerified", "presentable": false, "required": false, "system": false, "type": "bool"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation3429284244", "maxSelect": 1, "minSelect": 0, "name": "golVerifiedBy", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "select2453506101", "maxSelect": 1, "name": "containerType", "presentable": false, "required": false, "system": false, "type": "select", "values": ["General", "ODC/FR/OT", "<PERSON><PERSON>", "Mix"]}, {"hidden": false, "id": "select156841218", "maxSelect": 1, "name": "delayType", "presentable": false, "required": false, "system": false, "type": "select", "values": ["DPD", "Non-DPD"]}, {"hidden": false, "id": "number1885463975", "max": null, "min": null, "name": "preferableRate", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "number1241718566", "max": null, "min": null, "name": "containersPerMonth", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Pending", "Accepted", "Rejected", "In Progress", "Completed"]}, {"autogeneratePattern": "", "hidden": false, "id": "text1001949196", "max": 0, "min": 0, "name": "reason", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_417633717", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "custom_transport_job_order", "type": "base", "fields": [{"autogeneratePattern": "JOB-[0-9]{11}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "JOB-[0-9]{3,11}", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_721042516", "hidden": false, "id": "relation4113142680", "maxSelect": 1, "minSelect": 0, "name": "order", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "date3149528700", "max": "", "min": "", "name": "fromDate", "presentable": false, "required": false, "system": false, "type": "date"}, {"hidden": false, "id": "date301681072", "max": "", "min": "", "name": "toDate", "presentable": false, "required": false, "system": false, "type": "date"}, {"cascadeDelete": false, "collectionId": "pbc_2651147062", "hidden": false, "id": "relation1138201242", "maxSelect": 1, "minSelect": 0, "name": "serviceType", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_1602236899", "hidden": false, "id": "relation533621242", "maxSelect": 999, "minSelect": 0, "name": "vehicles", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text1156222427", "max": 0, "min": 0, "name": "remarks", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "file104153177", "maxSelect": 99, "maxSize": 0, "mimeTypes": [], "name": "files", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": [], "type": "file"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Pending", "Accepted", "Rejected", "In Progress", "Completed"]}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation3545646658", "maxSelect": 1, "minSelect": 0, "name": "created<PERSON>y", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_298834816", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "custom_transport_order_movement", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_721042516", "hidden": false, "id": "relation4113142680", "maxSelect": 1, "minSelect": 0, "name": "order", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_417633717", "hidden": false, "id": "relation3993090758", "maxSelect": 1, "minSelect": 0, "name": "jobOrder", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_1602236899", "hidden": false, "id": "relation461431942", "maxSelect": 1, "minSelect": 0, "name": "vehicle", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "json291929305", "maxSize": 0, "name": "driver", "presentable": false, "required": false, "system": false, "type": "json"}, {"hidden": false, "id": "date1269603864", "max": "", "min": "", "name": "startDate", "presentable": false, "required": false, "system": false, "type": "date"}, {"hidden": false, "id": "date826688707", "max": "", "min": "", "name": "endDate", "presentable": false, "required": false, "system": false, "type": "date"}, {"hidden": false, "id": "geoPoint593843763", "name": "startLocation", "presentable": false, "required": false, "system": false, "type": "geoPoint"}, {"hidden": false, "id": "geoPoint2041417573", "name": "currentLocation", "presentable": false, "required": false, "system": false, "type": "geoPoint"}, {"hidden": false, "id": "geoPoint1181702921", "name": "endLocation", "presentable": false, "required": false, "system": false, "type": "geoPoint"}, {"autogeneratePattern": "", "hidden": false, "id": "text1156222427", "max": 0, "min": 0, "name": "remarks", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Not Started", "In Transit", "Delivered", "Cancelled"]}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_721042516", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "custom_transport_orders", "type": "base", "fields": [{"autogeneratePattern": "ORD-[0-9]{11}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "ORD-[0-9]{3,11}", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text3733864923", "max": 0, "min": 0, "name": "consignee<PERSON><PERSON>", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text3096597736", "max": 0, "min": 0, "name": "cha<PERSON>ame", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_3190968249", "hidden": false, "id": "relation2462348188", "maxSelect": 1, "minSelect": 0, "name": "provider", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2168032777", "maxSelect": 1, "minSelect": 0, "name": "customer", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "date1269603864", "max": "", "min": "", "name": "startDate", "presentable": false, "required": false, "system": false, "type": "date"}, {"autogeneratePattern": "", "hidden": false, "id": "text593843763", "max": 0, "min": 0, "name": "startLocation", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "date826688707", "max": "", "min": "", "name": "endDate", "presentable": false, "required": false, "system": false, "type": "date"}, {"autogeneratePattern": "", "hidden": false, "id": "text1181702921", "max": 0, "min": 0, "name": "endLocation", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text208543272", "max": 0, "min": 0, "name": "specialRequest", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text228700180", "max": 0, "min": 0, "name": "vehicleDescription", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1005175516", "max": 0, "min": 0, "name": "orderDescription", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation3545646658", "maxSelect": 1, "minSelect": 0, "name": "created<PERSON>y", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text1001949196", "max": 0, "min": 0, "name": "reason", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Pending", "Accepted", "Rejected", "In Progress", "In Transit", "Delivered"]}, {"hidden": false, "id": "file104153177", "maxSelect": 99, "maxSize": 100000000, "mimeTypes": [], "name": "files", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": [], "type": "file"}, {"hidden": false, "id": "bool1531251113", "name": "merchantVerified", "presentable": false, "required": false, "system": false, "type": "bool"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2891322017", "maxSelect": 1, "minSelect": 0, "name": "merchantVerifiedBy", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "bool465722684", "name": "golVerified", "presentable": false, "required": false, "system": false, "type": "bool"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation3429284244", "maxSelect": 1, "minSelect": 0, "name": "golVerifiedBy", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_3224573581", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "custom_transport_service_requests", "type": "base", "fields": [{"autogeneratePattern": "REQ-[0-9]{11}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "REQ-[0-9]{3,11}", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2375276105", "maxSelect": 1, "minSelect": 0, "name": "user", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_721042516", "hidden": false, "id": "relation4113142680", "maxSelect": 1, "minSelect": 0, "name": "order", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_2651147062", "hidden": false, "id": "relation1138201242", "maxSelect": 1, "minSelect": 0, "name": "serviceType", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "bool1531251113", "name": "merchantVerified", "presentable": false, "required": false, "system": false, "type": "bool"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2891322017", "maxSelect": 1, "minSelect": 0, "name": "merchantVerifiedBy", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "bool465722684", "name": "golVerified", "presentable": false, "required": false, "system": false, "type": "bool"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation3429284244", "maxSelect": 1, "minSelect": 0, "name": "golVerifiedBy", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text3282145173", "max": 0, "min": 0, "name": "customerRemarks", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1001949196", "max": 0, "min": 0, "name": "reason", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Pending", "Accepted", "Rejected", "In Progress", "Completed"]}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_2981256593", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "custom_warehouse_job_order", "type": "base", "fields": [{"autogeneratePattern": "JOB-[0-9]{11}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "JOB-[0-9]{3,11}", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_2085983916", "hidden": false, "id": "relation4113142680", "maxSelect": 1, "minSelect": 0, "name": "order", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "date3149528700", "max": "", "min": "", "name": "fromDate", "presentable": false, "required": false, "system": false, "type": "date"}, {"hidden": false, "id": "date301681072", "max": "", "min": "", "name": "toDate", "presentable": false, "required": false, "system": false, "type": "date"}, {"cascadeDelete": false, "collectionId": "pbc_2651147062", "hidden": false, "id": "relation1138201242", "maxSelect": 1, "minSelect": 0, "name": "serviceType", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_1864144027", "hidden": false, "id": "relation2444010507", "maxSelect": 999, "minSelect": 0, "name": "containers", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text1156222427", "max": 0, "min": 0, "name": "remarks", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "file104153177", "maxSelect": 99, "maxSize": 0, "mimeTypes": [], "name": "files", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": [], "type": "file"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Pending", "Accepted", "Rejected", "In Progress", "Completed"]}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation3545646658", "maxSelect": 1, "minSelect": 0, "name": "created<PERSON>y", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_2172895503", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "custom_warehouse_order_movement", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_2085983916", "hidden": false, "id": "relation4113142680", "maxSelect": 1, "minSelect": 0, "name": "order", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "date2862495610", "max": "", "min": "", "name": "date", "presentable": false, "required": false, "system": false, "type": "date"}, {"autogeneratePattern": "", "hidden": false, "id": "text1156222427", "max": 0, "min": 0, "name": "remarks", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "file104153177", "maxSelect": 99, "maxSize": 0, "mimeTypes": [], "name": "files", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": [], "type": "file"}, {"autogeneratePattern": "", "hidden": false, "id": "text2063623452", "max": 0, "min": 0, "name": "status", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_2085983916", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "custom_warehouse_orders", "type": "base", "fields": [{"autogeneratePattern": "ORD-[0-9]{11}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "ORD-[0-9]{3,11}", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text118057898", "max": 0, "min": 0, "name": "igmNo", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1112657042", "max": 0, "min": 0, "name": "blNo", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1835792446", "max": 0, "min": 0, "name": "itemNo", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_1864144027", "hidden": false, "id": "relation2444010507", "maxSelect": 999, "minSelect": 0, "name": "containers", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text3733864923", "max": 0, "min": 0, "name": "consignee<PERSON><PERSON>", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text3096597736", "max": 0, "min": 0, "name": "cha<PERSON>ame", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_3190968249", "hidden": false, "id": "relation2462348188", "maxSelect": 1, "minSelect": 0, "name": "provider", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2168032777", "maxSelect": 1, "minSelect": 0, "name": "customer", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "date3149528700", "max": "", "min": "", "name": "fromDate", "presentable": false, "required": false, "system": false, "type": "date"}, {"hidden": false, "id": "date301681072", "max": "", "min": "", "name": "toDate", "presentable": false, "required": false, "system": false, "type": "date"}, {"autogeneratePattern": "", "hidden": false, "id": "text1005175516", "max": 0, "min": 0, "name": "orderDescription", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation3545646658", "maxSelect": 1, "minSelect": 0, "name": "created<PERSON>y", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Pending", "Accepted", "Rejected", "In Progress", "Completed"]}, {"hidden": false, "id": "file104153177", "maxSelect": 99, "maxSize": 100000000, "mimeTypes": [], "name": "files", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": [], "type": "file"}, {"hidden": false, "id": "bool1531251113", "name": "merchantVerified", "presentable": false, "required": false, "system": false, "type": "bool"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2891322017", "maxSelect": 1, "minSelect": 0, "name": "merchantVerifiedBy", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "bool465722684", "name": "golVerified", "presentable": false, "required": false, "system": false, "type": "bool"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation3429284244", "maxSelect": 1, "minSelect": 0, "name": "golVerifiedBy", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text1001949196", "max": 0, "min": 0, "name": "reason", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": ["CREATE UNIQUE INDEX `idx_KgEharWIIU` ON `custom_warehouse_orders` (\n  `igmNo`,\n  `blNo`,\n  `itemNo`\n)"], "system": false}, {"id": "pbc_2989885076", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "custom_warehouse_service_details", "type": "base", "fields": [{"autogeneratePattern": "REC-[0-9]{11}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "REC-[0-9]{3,11}", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_2085983916", "hidden": false, "id": "relation4113142680", "maxSelect": 1, "minSelect": 0, "name": "order", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_2981256593", "hidden": false, "id": "relation3993090758", "maxSelect": 1, "minSelect": 0, "name": "jobOrder", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_1864144027", "hidden": false, "id": "relation3349343259", "maxSelect": 1, "minSelect": 0, "name": "container", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_2651147062", "hidden": false, "id": "relation2363381545", "maxSelect": 1, "minSelect": 0, "name": "type", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text646683805", "max": 0, "min": 0, "name": "agent", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "date2862495610", "max": "", "min": "", "name": "date", "presentable": false, "required": false, "system": false, "type": "date"}, {"autogeneratePattern": "", "hidden": false, "id": "text1562571485", "max": 0, "min": 0, "name": "receiptNo", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1156222427", "max": 0, "min": 0, "name": "remarks", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "file104153177", "maxSelect": 99, "maxSize": 0, "mimeTypes": [], "name": "files", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": [], "type": "file"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Pending", "Accepted", "Rejected", "In Progress", "Completed"]}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_2169174847", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "custom_warehouse_service_requests", "type": "base", "fields": [{"autogeneratePattern": "REQ-[0-9]{11}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "REQ-[0-9]{3,11}", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2375276105", "maxSelect": 1, "minSelect": 0, "name": "user", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_2085983916", "hidden": false, "id": "relation4113142680", "maxSelect": 1, "minSelect": 0, "name": "order", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_2651147062", "hidden": false, "id": "relation1138201242", "maxSelect": 1, "minSelect": 0, "name": "serviceType", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "bool1531251113", "name": "merchantVerified", "presentable": false, "required": false, "system": false, "type": "bool"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2891322017", "maxSelect": 1, "minSelect": 0, "name": "merchantVerifiedBy", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "bool465722684", "name": "golVerified", "presentable": false, "required": false, "system": false, "type": "bool"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation3429284244", "maxSelect": 1, "minSelect": 0, "name": "golVerifiedBy", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text3282145173", "max": 0, "min": 0, "name": "customerRemarks", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1001949196", "max": 0, "min": 0, "name": "reason", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Pending", "Accepted", "Rejected", "In Progress", "Completed"]}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_2923026773", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "faqs", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text3069659470", "max": 0, "min": 0, "name": "question", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"convertURLs": false, "hidden": false, "id": "editor3671935525", "maxSize": 0, "name": "answer", "presentable": false, "required": false, "system": false, "type": "editor"}, {"hidden": false, "id": "json1874629670", "maxSize": 0, "name": "tags", "presentable": false, "required": false, "system": false, "type": "json"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_2605467279", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "messages", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_3419561403", "hidden": false, "id": "relation1704850090", "maxSelect": 1, "minSelect": 0, "name": "chat", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation1593854671", "maxSelect": 1, "minSelect": 0, "name": "sender", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text4274335913", "max": 0, "min": 0, "name": "content", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "file1204091606", "maxSelect": 1, "maxSize": 0, "mimeTypes": [], "name": "attachments", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": [], "type": "file"}, {"hidden": false, "id": "bool963269739", "name": "isRead", "presentable": false, "required": false, "system": false, "type": "bool"}, {"hidden": false, "id": "date3568618927", "max": "", "min": "", "name": "readAt", "presentable": false, "required": false, "system": false, "type": "date"}, {"hidden": false, "id": "select1219652353", "maxSelect": 1, "name": "messageType", "presentable": false, "required": false, "system": false, "type": "select", "values": ["text", "file"]}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_3190968249", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "service_provider", "type": "base", "fields": [{"autogeneratePattern": "SP-[0-9]{12}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "SP-[0-9]{2,12}", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_863811952", "hidden": false, "id": "relation3785202386", "maxSelect": 999, "minSelect": 0, "name": "service", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text724990059", "max": 0, "min": 0, "name": "title", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1843675174", "max": 0, "min": 0, "name": "description", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "file104153177", "maxSelect": 99, "maxSize": 100000000, "mimeTypes": [], "name": "files", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": [], "type": "file"}, {"autogeneratePattern": "", "hidden": false, "id": "text1587448267", "max": 0, "min": 0, "name": "location", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "json3217087507", "maxSize": 0, "name": "features", "presentable": false, "required": false, "system": false, "type": "json"}, {"autogeneratePattern": "", "hidden": false, "id": "text1281549880", "max": 0, "min": 0, "name": "contact", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "number2136230317", "max": null, "min": null, "name": "tariffRates", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "number1797807559", "max": null, "min": null, "name": "freeDays", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "number3371656666", "max": null, "min": null, "name": "monthlyDues", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "json1874629670", "maxSize": 0, "name": "tags", "presentable": false, "required": false, "system": false, "type": "json"}, {"hidden": false, "id": "number3632866850", "max": null, "min": null, "name": "rating", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation3182418120", "maxSelect": 1, "minSelect": 0, "name": "author", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "bool256245529", "name": "verified", "presentable": false, "required": false, "system": false, "type": "bool"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_863811952", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "services", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text724990059", "max": 0, "min": 0, "name": "title", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1843675174", "max": 0, "min": 0, "name": "description", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_2651147062", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "sub_services", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_863811952", "hidden": false, "id": "relation3785202386", "maxSelect": 1, "minSelect": 0, "name": "service", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text724990059", "max": 0, "min": 0, "name": "title", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1843675174", "max": 0, "min": 0, "name": "description", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_2696138989", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "ticket", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2168032777", "maxSelect": 1, "minSelect": 0, "name": "customer", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation646683805", "maxSelect": 1, "minSelect": 0, "name": "assigned_to", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text4224597626", "max": 0, "min": 0, "name": "subject", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"convertURLs": false, "hidden": false, "id": "editor1843675174", "maxSize": 0, "name": "description", "presentable": false, "required": false, "system": false, "type": "editor"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Open", "In_Progress", "Resolved", "Closed"]}, {"hidden": false, "id": "select1655102503", "maxSelect": 1, "name": "priority", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Low", "Medium", "High", "<PERSON><PERSON>"]}, {"hidden": false, "id": "bool1981675086", "name": "accepted", "presentable": false, "required": false, "system": false, "type": "bool"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_570658553", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "transport_job_order", "type": "base", "fields": [{"autogeneratePattern": "JOB-[0-9]{11}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "JOB-[0-9]{3,11}", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_1220228834", "hidden": false, "id": "relation4113142680", "maxSelect": 1, "minSelect": 0, "name": "order", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "date3149528700", "max": "", "min": "", "name": "fromDate", "presentable": false, "required": false, "system": false, "type": "date"}, {"hidden": false, "id": "date301681072", "max": "", "min": "", "name": "toDate", "presentable": false, "required": false, "system": false, "type": "date"}, {"cascadeDelete": false, "collectionId": "pbc_2651147062", "hidden": false, "id": "relation1138201242", "maxSelect": 1, "minSelect": 0, "name": "serviceType", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_1602236899", "hidden": false, "id": "relation533621242", "maxSelect": 999, "minSelect": 0, "name": "vehicles", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text1156222427", "max": 0, "min": 0, "name": "remarks", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "file104153177", "maxSelect": 99, "maxSize": 0, "mimeTypes": [], "name": "files", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": [], "type": "file"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Pending", "Accepted", "Rejected", "In Progress", "Completed"]}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation3545646658", "maxSelect": 1, "minSelect": 0, "name": "created<PERSON>y", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_2194712074", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "transport_order_movement", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_1220228834", "hidden": false, "id": "relation4113142680", "maxSelect": 1, "minSelect": 0, "name": "order", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_570658553", "hidden": false, "id": "relation3993090758", "maxSelect": 1, "minSelect": 0, "name": "jobOrder", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_1602236899", "hidden": false, "id": "relation461431942", "maxSelect": 1, "minSelect": 0, "name": "vehicle", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "json291929305", "maxSize": 0, "name": "driver", "presentable": false, "required": false, "system": false, "type": "json"}, {"hidden": false, "id": "date1269603864", "max": "", "min": "", "name": "startDate", "presentable": false, "required": false, "system": false, "type": "date"}, {"hidden": false, "id": "date826688707", "max": "", "min": "", "name": "endDate", "presentable": false, "required": false, "system": false, "type": "date"}, {"hidden": false, "id": "geoPoint593843763", "name": "startLocation", "presentable": false, "required": false, "system": false, "type": "geoPoint"}, {"hidden": false, "id": "geoPoint2041417573", "name": "currentLocation", "presentable": false, "required": false, "system": false, "type": "geoPoint"}, {"hidden": false, "id": "geoPoint1181702921", "name": "endLocation", "presentable": false, "required": false, "system": false, "type": "geoPoint"}, {"autogeneratePattern": "", "hidden": false, "id": "text1156222427", "max": 0, "min": 0, "name": "remarks", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Not Started", "In Transit", "Delivered", "Cancelled"]}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_1220228834", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "transport_orders", "type": "base", "fields": [{"autogeneratePattern": "ORD-[0-9]{11}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "ORD-[0-9]{3,11}", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text3733864923", "max": 0, "min": 0, "name": "consignee<PERSON><PERSON>", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text3096597736", "max": 0, "min": 0, "name": "cha<PERSON>ame", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_3190968249", "hidden": false, "id": "relation2462348188", "maxSelect": 1, "minSelect": 0, "name": "provider", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2168032777", "maxSelect": 1, "minSelect": 0, "name": "customer", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "date1269603864", "max": "", "min": "", "name": "startDate", "presentable": false, "required": false, "system": false, "type": "date"}, {"autogeneratePattern": "", "hidden": false, "id": "text593843763", "max": 0, "min": 0, "name": "startLocation", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "date826688707", "max": "", "min": "", "name": "endDate", "presentable": false, "required": false, "system": false, "type": "date"}, {"autogeneratePattern": "", "hidden": false, "id": "text1181702921", "max": 0, "min": 0, "name": "endLocation", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text208543272", "max": 0, "min": 0, "name": "specialRequest", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text228700180", "max": 0, "min": 0, "name": "vehicleDescription", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1005175516", "max": 0, "min": 0, "name": "orderDescription", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation3545646658", "maxSelect": 1, "minSelect": 0, "name": "created<PERSON>y", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text1001949196", "max": 0, "min": 0, "name": "reason", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Pending", "Accepted", "Rejected", "In Progress", "In Transit", "Delivered"]}, {"hidden": false, "id": "file104153177", "maxSelect": 99, "maxSize": 100000000, "mimeTypes": [], "name": "files", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": [], "type": "file"}, {"hidden": false, "id": "bool1531251113", "name": "merchantVerified", "presentable": false, "required": false, "system": false, "type": "bool"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2891322017", "maxSelect": 1, "minSelect": 0, "name": "merchantVerifiedBy", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "bool465722684", "name": "golVerified", "presentable": false, "required": false, "system": false, "type": "bool"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation3429284244", "maxSelect": 1, "minSelect": 0, "name": "golVerifiedBy", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_2240936223", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "transport_pricing_request", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2375276105", "maxSelect": 1, "minSelect": 0, "name": "user", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_3190968249", "hidden": false, "id": "relation3497959979", "maxSelect": 1, "minSelect": 0, "name": "serviceProvider", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "date1269603864", "max": "", "min": "", "name": "startDate", "presentable": false, "required": false, "system": false, "type": "date"}, {"autogeneratePattern": "", "hidden": false, "id": "text593843763", "max": 0, "min": 0, "name": "startLocation", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1181702921", "max": 0, "min": 0, "name": "endLocation", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text208543272", "max": 0, "min": 0, "name": "specialRequest", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "bool465722684", "name": "golVerified", "presentable": false, "required": false, "system": false, "type": "bool"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation3429284244", "maxSelect": 1, "minSelect": 0, "name": "golVerifiedBy", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "number1885463975", "max": null, "min": null, "name": "preferableRate", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "number1241718566", "max": null, "min": null, "name": "containersPerMonth", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Pending", "Accepted", "Rejected", "In Progress", "Completed"]}, {"autogeneratePattern": "", "hidden": false, "id": "text1001949196", "max": 0, "min": 0, "name": "reason", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_803993810", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "transport_service_requests", "type": "base", "fields": [{"autogeneratePattern": "REQ-[0-9]{11}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "REQ-[0-9]{3,11}", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_1220228834", "hidden": false, "id": "relation4113142680", "maxSelect": 1, "minSelect": 0, "name": "order", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2375276105", "maxSelect": 1, "minSelect": 0, "name": "user", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_2651147062", "hidden": false, "id": "relation1138201242", "maxSelect": 1, "minSelect": 0, "name": "serviceType", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "bool1531251113", "name": "merchantVerified", "presentable": false, "required": false, "system": false, "type": "bool"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2891322017", "maxSelect": 1, "minSelect": 0, "name": "merchantVerifiedBy", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "bool465722684", "name": "golVerified", "presentable": false, "required": false, "system": false, "type": "bool"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation3429284244", "maxSelect": 1, "minSelect": 0, "name": "golVerifiedBy", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text3282145173", "max": 0, "min": 0, "name": "customerRemarks", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text356646161", "max": 0, "min": 0, "name": "reason", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Pending", "Accepted", "Rejected", "In Progress", "Completed"]}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_2395140099", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "user_profile", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2375276105", "maxSelect": 1, "minSelect": 0, "name": "user", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text223244161", "max": 0, "min": 0, "name": "address", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1281549880", "max": 0, "min": 0, "name": "contact", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text755628466", "max": 0, "min": 0, "name": "businessName", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1291004845", "max": 0, "min": 0, "name": "gstIn", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1305771996", "max": 0, "min": 0, "name": "panNo", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "file2729472648", "maxSelect": 99, "maxSize": 100000000, "mimeTypes": [], "name": "documents", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": [], "type": "file"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_1602236899", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "vehicles", "type": "base", "fields": [{"autogeneratePattern": "VEH-[0-9]{11}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "VEH-[0-9]{3,11}", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2013763573", "maxSelect": 1, "minSelect": 0, "name": "ownedBy", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text948965009", "max": 0, "min": 0, "name": "vehicleNo", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1579384326", "max": 0, "min": 0, "name": "name", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Open", "Busy", "Damaged"]}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": ["CREATE UNIQUE INDEX `idx_uRRjC5qD6v` ON `vehicles` (`vehicleNo`)"], "system": false}, {"id": "pbc_2337621725", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "warehouse_job_order", "type": "base", "fields": [{"autogeneratePattern": "JOB-[0-9]{11}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "JOB-[0-9]{3,11}", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_504667162", "hidden": false, "id": "relation4113142680", "maxSelect": 1, "minSelect": 0, "name": "order", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "date3149528700", "max": "", "min": "", "name": "fromDate", "presentable": false, "required": false, "system": false, "type": "date"}, {"hidden": false, "id": "date301681072", "max": "", "min": "", "name": "toDate", "presentable": false, "required": false, "system": false, "type": "date"}, {"cascadeDelete": false, "collectionId": "pbc_2651147062", "hidden": false, "id": "relation1138201242", "maxSelect": 1, "minSelect": 0, "name": "serviceType", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_1864144027", "hidden": false, "id": "relation2444010507", "maxSelect": 999, "minSelect": 0, "name": "containers", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text1156222427", "max": 0, "min": 0, "name": "remarks", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "file104153177", "maxSelect": 99, "maxSize": 0, "mimeTypes": [], "name": "files", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": [], "type": "file"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Pending", "Accepted", "Rejected", "In Progress", "Completed"]}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation3545646658", "maxSelect": 1, "minSelect": 0, "name": "created<PERSON>y", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_312261765", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "warehouse_order_movement", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_504667162", "hidden": false, "id": "relation4113142680", "maxSelect": 1, "minSelect": 0, "name": "order", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "date2862495610", "max": "", "min": "", "name": "date", "presentable": false, "required": false, "system": false, "type": "date"}, {"autogeneratePattern": "", "hidden": false, "id": "text1156222427", "max": 0, "min": 0, "name": "remarks", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "file104153177", "maxSelect": 99, "maxSize": 0, "mimeTypes": [], "name": "files", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": [], "type": "file"}, {"autogeneratePattern": "", "hidden": false, "id": "text2063623452", "max": 0, "min": 0, "name": "status", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_504667162", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "warehouse_orders", "type": "base", "fields": [{"autogeneratePattern": "ORD-[0-9]{11}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "ORD-[0-9]{3,11}", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text118057898", "max": 0, "min": 0, "name": "igmNo", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1112657042", "max": 0, "min": 0, "name": "blNo", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text3145606124", "max": 0, "min": 0, "name": "itemNo", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_1864144027", "hidden": false, "id": "relation2444010507", "maxSelect": 999, "minSelect": 0, "name": "containers", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text3733864923", "max": 0, "min": 0, "name": "consignee<PERSON><PERSON>", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text3096597736", "max": 0, "min": 0, "name": "cha<PERSON>ame", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_3190968249", "hidden": false, "id": "relation2462348188", "maxSelect": 1, "minSelect": 0, "name": "provider", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2168032777", "maxSelect": 1, "minSelect": 0, "name": "customer", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "date3149528700", "max": "", "min": "", "name": "fromDate", "presentable": false, "required": false, "system": false, "type": "date"}, {"hidden": false, "id": "date301681072", "max": "", "min": "", "name": "toDate", "presentable": false, "required": false, "system": false, "type": "date"}, {"autogeneratePattern": "", "hidden": false, "id": "text1005175516", "max": 0, "min": 0, "name": "orderDescription", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation3545646658", "maxSelect": 1, "minSelect": 0, "name": "created<PERSON>y", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Pending", "Accepted", "Rejected", "In Progress", "Completed"]}, {"hidden": false, "id": "file104153177", "maxSelect": 99, "maxSize": 100000000, "mimeTypes": [], "name": "files", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": [], "type": "file"}, {"hidden": false, "id": "bool1531251113", "name": "merchantVerified", "presentable": false, "required": false, "system": false, "type": "bool"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2891322017", "maxSelect": 1, "minSelect": 0, "name": "merchantVerifiedBy", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "bool465722684", "name": "golVerified", "presentable": false, "required": false, "system": false, "type": "bool"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation3429284244", "maxSelect": 1, "minSelect": 0, "name": "golVerifiedBy", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text1001949196", "max": 0, "min": 0, "name": "reason", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": ["CREATE UNIQUE INDEX `idx_DNoVZImcwq` ON `warehouse_orders` (\n  `igmNo`,\n  `blNo`,\n  `itemNo`\n)"], "system": false}, {"id": "pbc_4161153972", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "warehouse_pricing_request", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2375276105", "maxSelect": 1, "minSelect": 0, "name": "user", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_3190968249", "hidden": false, "id": "relation3497959979", "maxSelect": 1, "minSelect": 0, "name": "serviceProvider", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "bool465722684", "name": "golVerified", "presentable": false, "required": false, "system": false, "type": "bool"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation3429284244", "maxSelect": 1, "minSelect": 0, "name": "golVerifiedBy", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "select2453506101", "maxSelect": 1, "name": "containerType", "presentable": false, "required": false, "system": false, "type": "select", "values": ["General", "ODC/FR/OT", "<PERSON><PERSON>", "Mix"]}, {"hidden": false, "id": "select156841218", "maxSelect": 1, "name": "delayType", "presentable": false, "required": false, "system": false, "type": "select", "values": ["DPD", "Non-DPD"]}, {"hidden": false, "id": "number1885463975", "max": null, "min": null, "name": "preferableRate", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "number1241718566", "max": null, "min": null, "name": "containersPerMonth", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Pending", "Accepted", "Rejected", "In Progress", "Completed"]}, {"autogeneratePattern": "", "hidden": false, "id": "text1001949196", "max": 0, "min": 0, "name": "reason", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_3217585111", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "warehouse_service_details", "type": "base", "fields": [{"autogeneratePattern": "REC-[0-9]{11}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "REC-[0-9]{3,11}", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_504667162", "hidden": false, "id": "relation4113142680", "maxSelect": 1, "minSelect": 0, "name": "order", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_2337621725", "hidden": false, "id": "relation3993090758", "maxSelect": 1, "minSelect": 0, "name": "jobOrder", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_1864144027", "hidden": false, "id": "relation3349343259", "maxSelect": 1, "minSelect": 0, "name": "container", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_2651147062", "hidden": false, "id": "relation2363381545", "maxSelect": 1, "minSelect": 0, "name": "type", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text646683805", "max": 0, "min": 0, "name": "agent", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "date2862495610", "max": "", "min": "", "name": "date", "presentable": false, "required": false, "system": false, "type": "date"}, {"autogeneratePattern": "", "hidden": false, "id": "text1562571485", "max": 0, "min": 0, "name": "receiptNo", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1156222427", "max": 0, "min": 0, "name": "remarks", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "file104153177", "maxSelect": 99, "maxSize": 0, "mimeTypes": [], "name": "files", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": [], "type": "file"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Pending", "Accepted", "Rejected", "In Progress", "Completed"]}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_1855067488", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "warehouse_service_requests", "type": "base", "fields": [{"autogeneratePattern": "REQ-[0-9]{11}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "REQ-[0-9]{3,11}", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2375276105", "maxSelect": 1, "minSelect": 0, "name": "user", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_504667162", "hidden": false, "id": "relation4113142680", "maxSelect": 1, "minSelect": 0, "name": "order", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_2651147062", "hidden": false, "id": "relation1138201242", "maxSelect": 1, "minSelect": 0, "name": "serviceType", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "bool1531251113", "name": "merchantVerified", "presentable": false, "required": false, "system": false, "type": "bool"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2891322017", "maxSelect": 1, "minSelect": 0, "name": "merchantVerifiedBy", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "bool465722684", "name": "golVerified", "presentable": false, "required": false, "system": false, "type": "bool"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation3429284244", "maxSelect": 1, "minSelect": 0, "name": "golVerifiedBy", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text3282145173", "max": 0, "min": 0, "name": "customerRemarks", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text356646161", "max": 0, "min": 0, "name": "reason", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Pending", "Accepted", "Rejected", "In Progress", "Completed"]}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_2689794359", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "warehouse_tariffs_request", "type": "base", "fields": [{"autogeneratePattern": "REQ-[0-9]{11}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "REQ-[0-9]{3,11}", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_504667162", "hidden": false, "id": "relation4113142680", "maxSelect": 1, "minSelect": 0, "name": "order", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_1864144027", "hidden": false, "id": "relation3349343259", "maxSelect": 1, "minSelect": 0, "name": "container", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "select2363381545", "maxSelect": 1, "name": "type", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Loaded", "<PERSON><PERSON><PERSON>"]}, {"hidden": false, "id": "date3149528700", "max": "", "min": "", "name": "fromDate", "presentable": false, "required": false, "system": false, "type": "date"}, {"hidden": false, "id": "date301681072", "max": "", "min": "", "name": "toDate", "presentable": false, "required": false, "system": false, "type": "date"}, {"autogeneratePattern": "", "hidden": false, "id": "text1156222427", "max": 0, "min": 0, "name": "remarks", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1001949196", "max": 0, "min": 0, "name": "reason", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "file104153177", "maxSelect": 99, "maxSize": 0, "mimeTypes": [], "name": "files", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": [], "type": "file"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Pending", "Accepted", "Rejected", "In Progress", "Completed"]}, {"hidden": false, "id": "bool1531251113", "name": "merchantVerified", "presentable": false, "required": false, "system": false, "type": "bool"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2891322017", "maxSelect": 1, "minSelect": 0, "name": "merchantVerifiedBy", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation3429284244", "maxSelect": 1, "minSelect": 0, "name": "golVerifiedBy", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "bool465722684", "name": "golVerified", "presentable": false, "required": false, "system": false, "type": "bool"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}]
import { NextResponse } from 'next/server';
import { getPocketBase } from '@/lib/pocketbase';

export async function POST(request) {
  try {
    const { clientId, orderId, orderNumber, subject } = await request.json();
    const pb = await getPocketBase();

    // Get the current user (customer)
    const authData = await pb.collection('users').authWithOAuth2Token(
      request.headers.get('authorization')?.split(' ')[1] || ''
    );
    
    if (!authData?.record) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const customerId = authData.record.id;

    // Check if chat already exists for this order
    const existingChats = await pb.collection('customer_client_chat').getList(1, 1, {
      filter: `customer = "${customerId}" && client = "${clientId}" && order = "${orderId}"`,
    });

    let chat;
    
    if (existingChats.items.length > 0) {
      // Return existing chat
      chat = existingChats.items[0];
    } else {
      // Create new chat
      chat = await pb.collection('customer_client_chat').create({
        customer: customerId,
        client: clientId,
        order: orderId,
        subject: subject || `Order #${orderNumber}`,
        status: 'active',
        lastMessage: 'Chat started',
        lastMessageAt: new Date().toISOString(),
        unreadCount: 0,
      });

      // Send initial message
      await pb.collection('customer_client_messages').create({
        chat: chat.id,
        sender: customerId,
        content: `Chat started regarding Order #${orderNumber}`,
        isRead: false,
      });
    }

    return NextResponse.json({
      chatId: chat.id,
      message: 'Chat started successfully',
    });
  } catch (error) {
    console.error('Error starting chat:', error);
    return NextResponse.json(
      { error: 'Failed to start chat' },
      { status: 500 }
    );
  }
}

'use client';

import { useSidebar } from "@/contexts/SidebarProvider";
import { useEffect } from "react";
import CFSOrderTable from "@/components/services/cfs/orders/CFSOrderTable";
import { useChat } from "@/hooks/useChat";
import { useRouter } from "next/navigation";

export default function RescanRequestPage() {
	const { setTitle } = useSidebar();
	const { getOrderClientChatSession, createOrderClientChatSession } = useChat();
	const router = useRouter();

	useEffect(() => {
		setTitle('My Orders')
	}, []);

	const handleChatWithClient = async (order) => {
		// Try to find existing open chat session for this order
		let session = await getOrderClientChatSession(order.id);
		if (session && session.status !== 'Close') {
			router.push(`/client/support/chat/${session.id}`);
			return;
		}
		// Otherwise, create a new chat session
		session = await createOrderClientChatSession(order.id, order.clientId);
		if (session) {
			router.push(`/client/support/chat/${session.id}`);
		}
	};

	return (
		<section className="grid gap-8">
			<CFSOrderTable onChatWithClient={handleChatWithClient} />
		</section>
	)
}

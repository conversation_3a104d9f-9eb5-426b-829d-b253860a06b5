// Sample user data for demonstration purposes
// This data structure matches the PocketBase users collection schema

const sampleData = [
  {
    id: 'user-001',
    name: '<PERSON><PERSON>',
    emailId: 'r<PERSON><PERSON>.<EMAIL>',
    phoneNo: '9876543210',
    status: 'Active',
    role: 'Customer',
    access: 'Customer Access',
    createdAt: '2024-01-15T10:30:00Z',
    username: 'r<PERSON><PERSON>_kumar',
    firstname: '<PERSON><PERSON>',
    lastname: '<PERSON>',
    verified: true,
    avatar: null
  },
  {
    id: 'user-002',
    name: '<PERSON><PERSON>',
    emailId: '<EMAIL>',
    phoneNo: '9876543211',
    status: 'Active',
    role: 'CFS Admin',
    access: 'Full CFS Access',
    createdAt: '2024-01-10T09:15:00Z',
    username: 'priya_sharma',
    firstname: '<PERSON>riya',
    lastname: '<PERSON>',
    verified: true,
    avatar: null
  },
  {
    id: 'user-003',
    name: '<PERSON><PERSON> <PERSON>',
    emailId: '<EMAIL>',
    phoneNo: '9876543212',
    status: 'Inactive',
    role: 'Customer',
    access: 'Customer Access',
    createdAt: '2024-01-05T14:20:00Z',
    username: 'amit_patel',
    firstname: 'Amit',
    lastname: 'Patel',
    verified: false,
    avatar: null
  },
  {
    id: 'user-004',
    name: 'Sunita Reddy',
    emailId: '<EMAIL>',
    phoneNo: '9876543213',
    status: 'Active',
    role: 'CFS Viewer',
    access: 'Read-only CFS Access',
    createdAt: '2024-01-12T11:45:00Z',
    username: 'sunita_reddy',
    firstname: 'Sunita',
    lastname: 'Reddy',
    verified: true,
    avatar: null
  },
  {
    id: 'user-005',
    name: 'Vikram Singh',
    emailId: '<EMAIL>',
    phoneNo: '9876543214',
    status: 'Blacklist',
    role: 'Customer',
    access: 'Customer Access',
    createdAt: '2024-01-08T16:30:00Z',
    username: 'vikram_singh',
    firstname: 'Vikram',
    lastname: 'Singh',
    verified: false,
    avatar: null
  },
  {
    id: 'user-006',
    name: 'Meera Joshi',
    emailId: '<EMAIL>',
    phoneNo: '9876543215',
    status: 'Active',
    role: 'CFS Admin',
    access: 'Full CFS Access',
    createdAt: '2024-01-20T08:00:00Z',
    username: 'meera_joshi',
    firstname: 'Meera',
    lastname: 'Joshi',
    verified: true,
    avatar: null
  },
  {
    id: 'user-007',
    name: 'Ravi Gupta',
    emailId: '<EMAIL>',
    phoneNo: '9876543216',
    status: 'Active',
    role: 'Customer',
    access: 'Customer Access',
    createdAt: '2024-01-18T13:15:00Z',
    username: 'ravi_gupta',
    firstname: 'Ravi',
    lastname: 'Gupta',
    verified: true,
    avatar: null
  },
  {
    id: 'user-008',
    name: 'Kavya Nair',
    emailId: '<EMAIL>',
    phoneNo: '9876543217',
    status: 'Inactive',
    role: 'CFS Viewer',
    access: 'Read-only CFS Access',
    createdAt: '2024-01-14T12:30:00Z',
    username: 'kavya_nair',
    firstname: 'Kavya',
    lastname: 'Nair',
    verified: false,
    avatar: null
  },
  {
    id: 'user-009',
    name: 'Arjun Mehta',
    emailId: '<EMAIL>',
    phoneNo: '9876543218',
    status: 'Active',
    role: 'Customer',
    access: 'Customer Access',
    createdAt: '2024-01-22T15:45:00Z',
    username: 'arjun_mehta',
    firstname: 'Arjun',
    lastname: 'Mehta',
    verified: true,
    avatar: null
  },
  {
    id: 'user-010',
    name: 'Deepika Rao',
    emailId: '<EMAIL>',
    phoneNo: '9876543219',
    status: 'Active',
    role: 'CFS Admin',
    access: 'Full CFS Access',
    createdAt: '2024-01-25T10:00:00Z',
    username: 'deepika_rao',
    firstname: 'Deepika',
    lastname: 'Rao',
    verified: true,
    avatar: null
  }
];

export default sampleData;

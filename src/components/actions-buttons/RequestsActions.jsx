import React from 'react'
import { Popover } from '../ui/Popover'
import { Circle<PERSON>heckBig, Download, EllipsisVertical, Eye, Trash } from 'lucide-react'
import Link from 'next/link';

export default function RequestsActions({
  row,
  redirectLink = '',
  RejectForm,
  user,
  EditForm,
  deleteItem,
  handleStatusUpdate
}) {
  return (
    <Popover
      trigger={<EllipsisVertical className="w-4 h-4" />}
      className='w-[250px] p-6'
    >
      <h1 className='font-semibold text-lg p-2 text-left border-b border-foreground/30'>Actions</h1>

      <Link href={redirectLink}>
        <button
          className='flex items-center justify-between gap-2 p-4 w-full cursor-pointer'
        >
          <p>View Details</p>
          <Eye
            size={18}
            className="cursor-pointer"
          />
        </button>
      </Link>

      <button
        className='flex items-center justify-between gap-2 p-4 w-full cursor-pointer'
      >
        <p>Download Zip</p>
        <Download
          size={18}
          className="cursor-pointer"
        />
      </button>

      {EditForm && <EditForm info={row.original} />}

      <button
        className='flex items-center justify-between gap-2 p-4 w-full cursor-pointer'
        onClick={async () => {
          console.log('Delete details for', row.original.id);
          const confirmation = confirm('Are you sure you want to delete this entry?');
          if (confirmation) {
            await deleteItem(row.original.id);
          }
        }}
      >
        <p>Delete Entry</p>
        <Trash
          size={18}
          className="cursor-pointer"
        />
      </button>
      {
        (user?.role === 'Root' || user?.role === 'Merchant') && (
          <>
            <button
              className='flex items-center justify-between gap-2 p-4 w-full cursor-pointer'
              onClick={() => handleStatusUpdate(
                row.original.id,
                'Accepted'
              )}
            >
              <p>Accept Request</p>
              <CircleCheckBig
                size={18}
                className="cursor-pointer"
              />
            </button>

            <RejectForm info={row.original} />
          </>
        )
      }
    </Popover>
  )
}


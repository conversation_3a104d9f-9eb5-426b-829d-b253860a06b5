import { useState } from "react";
import { Upload, Plus } from "lucide-react";
import Button from "@/components/ui/Button";
import { Dialog } from "@/components/ui/Dialog";
import Input from "@/components/ui/Input";
import Label from "@/components/ui/Label";
import { useCollection } from "@/hooks/useCollection";
import { toast } from "sonner";
import TextArea from "@/components/ui/TextArea";
import JobOrderInput from "../JobOrderInput";
import VehicleInput from "../VehicleInput";
import OrderInput from "../OrderInput";

export default function Form() {
	const { createItem, mutation } = useCollection('transport_order_movement');
	const [formData, setFormData] = useState({
		driver_name: '',
		driver_contact: '',
		startDate: new Date().toISOString().split('T')[0],
		status: 'Not Started',
		remarks: '',
		files: []
	});
	const [orderId, setOrderId] = useState('');
	const [jobOrderId, setJobOrderId] = useState('')
	const [vehicleNo, setVehicleNo] = useState('')
	const [isOpen, setIsOpen] = useState(false);

	const handleChange = (e) => {
		const { name, value } = e.target;
		setFormData((prev) => ({
			...prev,
			[name]: value,
		}));
	};

	const handleReset = () => {
		setFormData({
			driver_name: '',
			driver_contact: '',
			startDate: new Date().toISOString().split('T')[0],
			status: 'Not Started',
			remarks: '',
			files: []
		});
		setOrderId('');
		setJobOrderId('');
		setVehicleNo('');
	};

	const handleFileChange = (e) => {
		const files = Array.from(e.target.files);
		setFormData((prev) => ({
			...prev,
			files
		}));
	};

	const handleSubmit = async (e) => {
		e.preventDefault();
		try {
			const data = new FormData();
			data.append('order', orderId);
			data.append('jobOrder', jobOrderId);
			data.append('vehicle', vehicleNo);
			data.append('driver', JSON.stringify({ name: formData.driver_name, contact: formData.driver_contact, }));
			data.append('startDate', formData.startDate);
			data.append('remarks', formData.remarks);
			data.append('status', formData.status);

			// Append each file
			formData.files.forEach((file) => {
				data.append('files', file); // `files` must match the PocketBase field name
			});

			console.log('Form submitted:', data);
			await createItem(data);
			toast.success('Created a new entry');
		} catch (error) {
			console.log(error)
			toast.error(error.message);
		} finally {
			handleReset();
			mutation();
			setIsOpen(false);
		}
	};

	return (
		<Dialog
			open={isOpen}
			onOpenChange={setIsOpen}
			trigger={
				<Button
					title={'Add Movement'}
					icon={<Plus className='w-5 h-5' />}
					iconPosition='right'
					className='rounded-md'
					textSize='text-sm'
				/>
			}
			title="Create a New Order Movement"
			className='bg-accent'
		>
			<div className="grid gap-4 md:min-w-[40dvw] min-w-[80dvw]">

				<OrderInput setOrderId={setOrderId} />
				<JobOrderInput setOrderId={setJobOrderId} />
				<VehicleInput setVehicleId={setVehicleNo} />

				<div className='flex flex-col gap-2'>
					<Label title="Start Date" />
					<Input
						type="date"
						name="startDate"
						value={formData.startDate}
						onChange={handleChange}
						placeholder="Select date"
						className="bg-accent"
					/>
				</div>

				<div className='flex flex-col gap-2'>
					<Label title="Driver Name" />
					<Input
						type="text"
						name="driver_name"
						value={formData.driver_name}
						onChange={handleChange}
						placeholder="eg; John Doe, etc."
						className="bg-accent"
					/>
				</div>

				<div className='flex flex-col gap-2'>
					<Label title="Driver Contact" />
					<Input
						type="text"
						name="driver_contact"
						value={formData.driver_contact}
						onChange={handleChange}
						placeholder="eg; 1234567890, etc."
						className="bg-accent"
					/>
				</div>

				<div className="flex flex-col gap-2">
					<Label title="Remarks" />
					<TextArea
						name="remarks"
						placeholder="Optional remarks..."
						value={formData.remarks}
						onChange={handleChange}
						className="bg-accent"
					/>

					<div className='flex flex-col gap-2 mt-4'>
						<Label title={'Upload Documents'} />
						<div className="flex items-center gap-2 mt-2">
							<label className="flex items-center cursor-pointer border rounded-xl px-4 py-2 bg-accent">
								<svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
									<path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
								</svg>
								<span className='text-sm'>Choose File</span>
								<input
									type="file"
									className="hidden"
									multiple
									onChange={handleFileChange}
								/>
							</label>
							<span className="ml-2 text-sm text-gray-500">
								{formData.files.length > 0
									? formData.files.map((file) => file.name).join(', ')
									: 'No files chosen'}
							</span>
						</div>
					</div>
				</div>

				<div className="mt-6">
					<Button onClick={handleSubmit} title="Add Movement" icon={<Upload />} iconPosition="right" className="rounded-xl" />
				</div>
			</div>
		</Dialog>
	);
}

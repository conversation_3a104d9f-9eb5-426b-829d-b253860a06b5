import { DataTable } from '@/components/ui/Table';
import { <PERSON><PERSON>heckBig, Eye, Trash, } from 'lucide-react';
import { useCollection } from '@/hooks/useCollection';
import EditForm from './EditForm';
import { useAuth } from '@/contexts/AuthContext';
import MobileDataTable from '@/components/ui/MobileDataTable';
import { useIsMobile } from '@/hooks/use-mobile';
import { useEffect, useState } from 'react';
import Stats from './Stats';
import RejectForm from './RejectForm';
import { toast } from 'sonner';
import RequestsActions from '@/components/actions-buttons/RequestsActions';

export default function WarehousePricingsRequests() {
	const { data, deleteItem, updateItem, mutation } = useCollection('warehouse_pricing_request', {
		expand: 'user,serviceProvider'
	});
	const [filteredData, setFilteredData] = useState([]);
	const [filteredColumns, setFilteredColumns] = useState([]);
	const [displayData, setDisplayData] = useState([]);
	const { user } = useAuth();

	const handleStatusUpdate = async (id, status = 'Pending') => {
		try {
			switch (user?.role) {
				case 'Root':
					await updateItem(id, {
						status: status,
						golVerified: true,
						golVerifiedBy: user?.id
					});
					toast.success('Updated');
					break;
				case 'Merchant':
					await updateItem(id, {
						status: status,
						merchantVerified: true,
						merchantVerifiedBy: user?.id
					});
					toast.success('Updated');
					break;
				default:
					break;
			}
		} catch (error) {
			console.log(error)
			toast.error(error.message);
		} finally {
			mutation()
		}
	}

	const columns = [
		{
			id: 'User',
			accessorKey: 'expand.user.username',
			header: 'User',
			filterable: true,
			cell: ({ row }) => <div>{row.original?.expand?.user?.username}</div>,
		},
		{
			id: 'serviceProvider',
			accessorKey: 'expand.serviceProvider.title',
			header: 'Service Provider',
			filterable: true,
			cell: ({ row }) => <div>{row.original?.expand?.serviceProvider?.title}</div>,
		},
		{
			id: 'preferableRate',
			accessorKey: 'preferableRate',
			header: 'Preferable Rate',
			filterable: true,
			cell: ({ row }) => <div> Rs. {row.original?.preferableRate?.toLocaleString()} </div>,
		},
		{
			id: 'delayType',
			accessorKey: 'delayType',
			header: 'DPD / Non-DPD',
			filterable: true,
			cell: ({ row }) => <div> {row.original?.delayType} </div>,
		},
		{
			id: 'containerType',
			accessorKey: 'containerType',
			header: 'Container Type',
			filterable: true,
			cell: ({ row }) => <div> {row.original?.containerType?.toLocaleString()} </div>,
		},
		{
			id: 'containersPerMonth',
			accessorKey: 'containersPerMonth',
			header: 'Containers Per Month',
			filterable: true,
			cell: ({ row }) => <div> {row.original?.containersPerMonth?.toLocaleString()} </div>,
		},
		{
			id: 'status',
			accessorKey: 'status',
			header: 'Status',
			filterable: true,
			cell: ({ row }) => <div className={`${getStatusColor(row.original.status)} rounded-xl px-4 py-2 text-center`}>{row.original.status}</div>,
		},
		{
			id: 'reason',
			accessorKey: 'reason',
			header: 'Reason',
			filterable: true,
			cell: ({ row }) => <div>{row.original?.reason}</div>,
		},
		{
			id: 'actions',
			accessorKey: 'actions',
			header: 'Actions',
			filterable: false,
			cell: ({ row }) => (
        <RequestsActions
          row={row}
          EditForm={EditForm}
          handleStatusUpdate={handleStatusUpdate}
          RejectForm={RejectForm}
          deleteItem={deleteItem}
          user={user} 
        />
			),
		}
	];

	const getStatusColor = (status) => {
		switch (status) {
			case 'Accepted':
				return 'bg-green-100 text-green-800';
			case 'Pending':
				return 'bg-yellow-100 text-yellow-800';
			case 'Rejected':
				return 'bg-red-100 text-red-800';
			default:
				return 'bg-gray-100 text-gray-800';
		}
	};

	useEffect(() => {
		if (data?.length > 0 && user?.id) {
			let filtered_data = [];
			let filtered_cols = [];
			switch (user?.role) {
				case 'Customer':
					filtered_data = data.filter((item) => item?.user === user?.id);
					filtered_cols = columns.filter((item) => item?.id !== 'User')
					break;
				case 'Root':
					filtered_data = data;
					filtered_cols = columns;
					break;
				default:
					break;
			}
			setFilteredData(filtered_data);
			setFilteredColumns(filtered_cols);
			setDisplayData(filtered_data);
		}
	}, [data, user]);


	return (
		<>
			<Stats original={filteredData} requests={displayData} setRequests={setDisplayData} />
			<div className="border-2 md:bg-accent md:p-4 rounded-xl mt-4">
				<h1 className="text-xl font-semibold p-4">Requests List</h1>
				{
					useIsMobile() ? (
						<MobileDataTable
							columns={filteredColumns}
							data={displayData}
						/>
					) : (
						<DataTable
							columns={filteredColumns}
							data={displayData}
							displayFilters={true}
						/>
					)
				}
			</div>
		</>
	);
}

